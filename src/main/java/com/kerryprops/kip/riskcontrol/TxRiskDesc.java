package com.kerryprops.kip.riskcontrol.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.ArrayUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * TxRiskDesc.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-05-22 16:49:49
 **/
@Getter
@Setter
public class TxRiskDesc {

    /**
     * 风险类型代码与描述的映射关系.
     */
    private static final Map<Long, String> RISK_TYPE_MAP = buildRiskDesc();

    @Schema(title = "腾讯风控类型")
    private Long code;

    @Schema(title = "腾讯风控类型对于描述")
    private String desc;

    /**
     * 根据风险类型代码创建风险描述.
     *
     * @param codes 风险类型代码.
     * @return 包含风险类型代码和对应描述实例.
     */
    public static List<TxRiskDesc> buildDescriptions(Long[] codes) {
        if (ArrayUtils.isEmpty(codes)) {
            return List.of();
        }
        return Arrays.stream(codes)
                .filter(Objects::nonNull)
                .map(code -> {
                    TxRiskDesc remark = new TxRiskDesc();
                    remark.setCode(code);
                    remark.setDesc(RISK_TYPE_MAP.get(code));
                    return remark;
                })
                .toList();
    }

    private static Map<Long, String> buildRiskDesc() {
        Map<Long, String> riskDesc = new HashMap<>();
        riskDesc.put(1L, "账号信用低，账号近期存在因恶意被处罚历史，网络低活跃，被举报等因素");
        riskDesc.put(11L, "疑似低活跃账号，账号活跃度与正常用户有差异");
        riskDesc.put(2L, "垃圾账号，疑似批量注册小号，近期存在严重违规或大量举报");
        riskDesc.put(21L, "疑似小号，账号有疑似线上养号，小号等行为");
        riskDesc.put(22L, "疑似违规账号，账号曾有违规行为、曾被举报过、曾因违规被处罚过等");
        riskDesc.put(3L,
                "无效账号，送检账号参数无法成功解析，请检查微信OpenId是否有误/AppId与QQ OpenId无法关联/微信OpenId权限是否开通/手机号是否为中国大陆手机号");
        riskDesc.put(4L, "黑名单，该账号在业务侧有过拉黑记录");
        riskDesc.put(5L, "白名单，业务自行有添加过白名单记录");
        riskDesc.put(101L, "批量操作，存在IP/设备/环境等因素的聚集性异常");
        riskDesc.put(1011L, "疑似IP属性聚集，出现IP聚集");
        riskDesc.put(1012L, "疑似设备属性聚集，出现设备聚集");
        riskDesc.put(102L, "自动机，疑似自动机批量请求");
        riskDesc.put(103L, "恶意行为-网赚，疑似网赚");
        riskDesc.put(104L, "微信登录态无效，检查WeChatAccessToken参数，是否已经失效");
        riskDesc.put(201L, "环境风险，环境异常操作IP/设备/环境存在异常。当前IP为非常用IP或恶意IP段");
        riskDesc.put(2011L, "疑似非常用IP，请求当前请求IP非该账号常用IP");
        riskDesc.put(2012L, "疑似IP异常，使用IDC机房IP或使用代理IP或使用恶意IP等");
        riskDesc.put(205L, "非公网有效IP，传进来的IP地址为内网IP地址或者IP保留地址");
        return riskDesc;
    }


}