package com.kerryprops.kip.riskcontrol.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * TxRiskSceneCode.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-05-21 11:17:49
 **/
@RequiredArgsConstructor
@Getter
public enum TxRiskSceneCode {
    /**
     * 活动防刷默认场景码.
     */
    ACTIVITY_ANTI_RUSH("e_activity_antirush"),

    /**
     * 登录保护默认场景码.
     */
    LOGIN("e_login_protection"),

    /**
     * 注册保护默认场景码.
     */
    REGISTER("e_register_protection");

    private final String txSceneCode;
}
