package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.exception.BadRequestException;
import com.kerryprops.kip.riskcontrol.constant.RiskLevel;
import com.kerryprops.kip.riskcontrol.constant.RiskType;
import com.kerryprops.kip.riskcontrol.dto.ComprehensiveRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.ComprehensiveRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.dto.PhoneRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.PhoneTxRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.service.ComprehensiveRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.vo.PhoneRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.vo.TxRiskResponse;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Controller for risk assessment API endpoints.
 * Provides comprehensive risk assessment capabilities including phone number validation,
 * IP geolocation-based assessment, and Tencent Cloud risk evaluation.
 *
 * <p>For detailed documentation, see: https://kerryprops.atlassian.net/wiki/spaces/TAIC/pages/675840071/S+-
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/risk")
@Tag(name = "Kerry风险评估", description = "Kerry Properties风险评估服务，包括手机号风险评估、IP地址地理位置风险评估和腾讯云风控服务")
public class RiskAssessmentController {

    private final RiskAssessmentService riskAssessmentService;

    private final TencentCloudService tencentCloudService;

    @Autowired(required = false)
    private ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    public RiskAssessmentController(RiskAssessmentService riskAssessmentService, TencentCloudService tencentCloudService) {
        this.riskAssessmentService = riskAssessmentService;
        this.tencentCloudService = tencentCloudService;
    }

    /**
     * Endpoint to assess risk for phone numbers.
     *
     * @param request       the request containing phone numbers to assess
     * @param bindingResult validation result
     * @return response entity with assessment results or error
     */
    @Operation(summary = "批量手机号风险评估", description = "通过是否高危虚拟号评估风险")
    @PostMapping("/assess")
    public List<PhoneRiskAssessResponse> assessPhoneNumbers(@Valid @NotNull @RequestBody PhoneRiskAssessRequest request,
                                                            BindingResult bindingResult) {

        log.info("Received risk assessment request for {} phone numbers", Optional.of(request.getPhoneNumbers())
                .map(List::size)
                .orElse(0));

        // Validate request for NotEmpty
        if (bindingResult.hasErrors()) {
            var errorMessage = bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("Invalid request");

            log.warn("Validation error: {}", errorMessage);
            throw new BadRequestException();
        }

        // Validate phone numbers according to the new pattern
        List<PhoneRiskAssessResponse> results = new ArrayList<>();
        for (String originPhoneNumber : request.getPhoneNumbers()) {
            String phoneNumber = StringUtils.deleteWhitespace(originPhoneNumber);
            if (isValidPhoneNumber(phoneNumber)) {
                // Process valid phone numbers with the service
                results.add(riskAssessmentService.assessPhoneNumber(phoneNumber));
                log.info("Processing valid phone number: {}", phoneNumber);
            } else {
                // Return LOW risk level for all invalid phone numbers
                // This includes numbers with different lengths or not starting with 1
                results.add(createLowRiskResponse(phoneNumber));
                log.warn("Invalid phone number format: {}, returning LOW risk level", phoneNumber);
            }
        }

        log.info("Successfully processed risk assessment for {} phone numbers", results.size());
        return results;
    }

    @Operation(summary = "腾讯云风控", description = "通过腾讯风控引擎对用户信息进行风控评估")
    @PostMapping("/tencent-assess")
    public TxRiskResponse assessPhoneNumber(@Valid @RequestBody PhoneTxRiskAssessRequest request)
            throws TencentCloudSDKException {
        return tencentCloudService.assessPhoneRisk(request);
    }


    /**
     * 综合风控评估端点.
     * 基于业务规则表实现的完整风控评估功能，支持多种业务场景和风控条件.
     *
     * @deprecated 此端点已废弃。请使用以下新的场景特定端点
     * @param request       综合风控评估请求
     * @param bindingResult 验证结果
     * @return ComprehensiveRiskAssessResponse 包含详细的风控评估结果
     */
    @Deprecated(since = "2.0.0", forRemoval = true)
    @Operation(
            summary = "综合风控评估（已废弃，请使用新的场景特定端点）",
            description = """
                    此端点已废弃。请使用以下新的场景特定端点：
                    - /api/risk-assessment/login - 登录场景
                    - /api/risk-assessment/parking-benefits - 会员停车权益
                    - /api/risk-assessment/photo-points - 拍照积分
                    - /api/risk-assessment/marketing - 营销场景
                    - /api/risk-assessment/sales-points - 销售积分
                    - /api/risk-assessment/coupon-verification - 核销券
                    """
    )
    @PostMapping("/comprehensive-assess")
    public ComprehensiveRiskAssessResponse assessComprehensiveRisk(
            @Valid @NotNull @RequestBody ComprehensiveRiskAssessRequest request,
            BindingResult bindingResult) {

        log.info("收到综合风控评估请求 - 业务场景: {}, 商场: {}, IP: {}",
                request.getBusinessScenario(), request.getMallCode(), request.getIpAddress());

        // 验证请求参数
        if (bindingResult.hasErrors()) {
            var errorMessage = bindingResult.getAllErrors()
                    .stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("请求参数验证失败");

            log.warn("综合风控评估参数验证错误: {}", errorMessage);
            throw new BadRequestException();
        }

        try {
            // 检查综合风控评估服务是否可用
            if (comprehensiveRiskAssessmentService == null) {
                log.warn("综合风控评估服务不可用 (Drools已禁用) - 业务场景: {}", request.getBusinessScenario());

                // 返回默认通过响应
                ComprehensiveRiskAssessResponse defaultResponse = new ComprehensiveRiskAssessResponse();
                defaultResponse.setBusinessScenario(request.getBusinessScenario());
                defaultResponse.setMallCode(request.getMallCode());
                defaultResponse.setUserId(request.getUserId());
                defaultResponse.setIpAddress(request.getIpAddress());
                defaultResponse.setRiskResult(com.kerryprops.kip.riskcontrol.constant.RiskResult.PASS);
                defaultResponse.setBlockMessage(null);
                defaultResponse.setAssessmentDetails("综合风控评估服务不可用，默认通过");
                defaultResponse.setAssessmentTime(java.time.LocalDateTime.now());

                return defaultResponse;
            }

            // 创建风控评估上下文
            var context = createRiskAssessmentContext(request);

            // 执行综合风控评估
            var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

            // 转换为响应DTO
            ComprehensiveRiskAssessResponse response = ComprehensiveRiskAssessResponse.fromContext(assessedContext);

            log.info("综合风控评估完成 - 业务场景: {}, 结果: {}, 详情: {}",
                    request.getBusinessScenario(), response.getRiskResult(), response.getAssessmentDetails());

            return response;

        } catch (IllegalArgumentException | NullPointerException e) {
            log.error("综合风控评估异常 - 业务场景: {}, 错误: {}", request.getBusinessScenario(), e.getMessage(), e);

            // 返回默认拒绝响应
            ComprehensiveRiskAssessResponse errorResponse = new ComprehensiveRiskAssessResponse();
            errorResponse.setBusinessScenario(request.getBusinessScenario());
            errorResponse.setMallCode(request.getMallCode());
            errorResponse.setUserId(request.getUserId());
            errorResponse.setIpAddress(request.getIpAddress());
            errorResponse.setRiskResult(com.kerryprops.kip.riskcontrol.constant.RiskResult.REJECT);
            errorResponse.setBlockMessage("系统繁忙，请稍后重试");
            errorResponse.setAssessmentDetails("系统异常：" + e.getMessage());
            errorResponse.setAssessmentTime(java.time.LocalDateTime.now());

            return errorResponse;
        }
    }

    /**
     * 从请求创建风控评估上下文.
     *
     * @param request 综合风控评估请求
     * @return RiskAssessmentContext
     */
    private com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext createRiskAssessmentContext(
            ComprehensiveRiskAssessRequest request) {
        // 解析业务场景
        var businessScenario = parseBusinessScenario(request.getBusinessScenario());

        // 解析商场代码
        var mallCode = parseMallCode(request.getMallCode());

        // 创建基础上下文
        var context = com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.create(businessScenario, mallCode);

        // 设置用户信息
        context.setUserId(request.getUserId());
        context.setUnionId(request.getUnionId());

        // 设置IP信息
        if (request.getIpAddress() != null) {
            context.withIpInfo(request.getIpAddress(), null);
        }

        // 设置手机号信息
        if (request.getPhoneNumber() != null) {
            context.withPhoneInfo(request.getPhoneNumber(), null, null);
        }

        // 设置会员信息
        context.withMemberInfo(request.getMemberPoints(), request.getIsFrozenMember());

        // 设置风控信息
        context.withRiskInfo(request.getTencentRiskLevel(),
                request.getUnionIdLoginCount(),
                request.getPhoneNumberLoginCount());

        return context;
    }

    /**
     * 解析业务场景.
     */
    private com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.BusinessScenario 
            parseBusinessScenario(String scenario) {
        try {
            return com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.BusinessScenario
                    .valueOf(scenario);
        } catch (IllegalArgumentException e) {
            log.warn("未知的业务场景: {}, 使用默认值LOGIN", scenario);
            return com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.BusinessScenario
                    .LOGIN;
        }
    }

    /**
     * 解析商场代码.
     */
    private com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.MallCode 
            parseMallCode(String mallCode) {
        try {
            return com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.MallCode
                    .valueOf(mallCode);
        } catch (IllegalArgumentException e) {
            log.warn("未知的商场代码: {}, 使用默认值ALL", mallCode);
            return com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext.MallCode.ALL;
        }
    }

    // Removed unused method isExactly11DigitsNotStartingWith1 as we now return LOW risk for all invalid formats

    /**
     * Validates if a phone number matches the required pattern.
     * Valid phone numbers must:
     * 1. Have an optional +86 or 86 prefix (+ is optional in +86)
     * 2. Be 11 digits without prefix
     * 3. Start with 1 as the first digit of the 11-digit number
     *
     * @param phoneNumber the phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return false;
        }
        return PhoneNumberUtil.PHONE_PATTERN.matcher(phoneNumber)
                .matches();
    }

    /**
     * Creates a response with LOW risk level for invalid phone numbers.
     * Handles empty or null phone numbers by using empty string as a default.
     *
     * @param phoneNumber the phone number, can be null or empty
     * @return the risk assessment response with LOW risk
     */
    private PhoneRiskAssessResponse createLowRiskResponse(String phoneNumber) {
        // Use empty string if phoneNumber is null to avoid NullPointerException
        String safePhoneNumber = phoneNumber != null ? phoneNumber : "";

        return new PhoneRiskAssessResponse(safePhoneNumber, RiskLevel.LOW, Collections.singletonList(RiskType.NONE),
                LocalDateTime.now());
    }

}
