package com.kerryprops.kip.riskcontrol.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 综合风控评估请求DTO.
 * 支持多种业务场景的风控评估.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "综合风控评估请求")
public class ComprehensiveRiskAssessRequest {

    /**
     * 业务场景.
     */
    @NotBlank(message = "业务场景不能为空")
    @Schema(
            description = "业务场景",
            example = "LOGIN",
            allowableValues = {"LOGIN", "PARKING_BENEFIT", "PHOTO_POINTS", "MARKETING", 
                    "SALES_POINTS", "COUPON_VERIFICATION"},
            required = true
    )
    private String businessScenario;

    /**
     * 商场代码.
     */
    @NotBlank(message = "商场代码不能为空")
    @Schema(
            description = "商场代码",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"},
            required = true
    )
    private String mallCode;

    /**
     * 用户ID.
     */
    @Schema(description = "用户ID", example = "user123")
    private String userId;

    /**
     * UnionID.
     */
    @Schema(description = "微信UnionID", example = "unionid123")
    private String unionId;

    /**
     * IP地址.
     */
    @Schema(description = "用户IP地址", example = "************")
    private String ipAddress;

    /**
     * 手机号.
     */
    @Schema(description = "用户手机号", example = "***********")
    private String phoneNumber;

    /**
     * 会员积分.
     */
    @Schema(description = "会员积分", example = "1000")
    private Integer memberPoints;

    /**
     * 是否为冻结会员.
     */
    @Schema(description = "是否为冻结会员", example = "false")
    private Boolean isFrozenMember;

    /**
     * 腾讯风控等级.
     */
    @Schema(
            description = "腾讯风控评估等级",
            example = "低",
            allowableValues = {"高", "中", "低"}
    )
    private String tencentRiskLevel;

    /**
     * 一个月内UnionID登录次数.
     */
    @Schema(description = "一个月内UnionID登录次数", example = "0")
    private Integer unionIdLoginCount;

    /**
     * 一个月内手机号登录次数.
     */
    @Schema(description = "一个月内手机号登录次数", example = "2")
    private Integer phoneNumberLoginCount;
}
