package com.kerryprops.kip.riskcontrol;

import com.kerryprops.kip.common.JsonUtils;
import com.kerryprops.kip.exception.ApiAccessException;
import com.kerryprops.kip.riskcontrol.config.TencentCloudProperties;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.constant.TxRiskAccountType;
import com.kerryprops.kip.riskcontrol.dto.PhoneTxRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.vo.TxRiskDesc;
import com.kerryprops.kip.riskcontrol.vo.TxRiskResponse;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.rce.v20201103.RceClient;
import com.tencentcloudapi.rce.v20201103.models.AccountInfo;
import com.tencentcloudapi.rce.v20201103.models.DataAuthorizationInfo;
import com.tencentcloudapi.rce.v20201103.models.InputManageMarketingRisk;
import com.tencentcloudapi.rce.v20201103.models.ManageMarketingRiskRequest;
import com.tencentcloudapi.rce.v20201103.models.ManageMarketingRiskResponse;
import com.tencentcloudapi.rce.v20201103.models.OtherAccountInfo;
import com.tencentcloudapi.rce.v20201103.models.OutputManageMarketingRisk;
import com.tencentcloudapi.rce.v20201103.models.WeChatAccountInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.time.Instant;

@Slf4j
@Component
@RequiredArgsConstructor
public class TencentCloudService {

    private final RceClient rceClient;

    private final TencentCloudProperties tencentCloudProperties;

    /**
     * 评估手机号码的风险.
     * <a href="https://cloud.tencent.com/document/product/1110/36828">接口文档</a>
     *
     * @param request 包含待评估手机号的请求参数对象
     * @return 风险评估结果对象，表示具体风险等级
     * @throws TencentCloudSDKException 如果调用腾讯云风险控制服务时发生异常
     */
    public TxRiskResponse assessPhoneRisk(PhoneTxRiskAssessRequest request) throws TencentCloudSDKException {
        ManageMarketingRiskRequest riskRequest = buildRiskRequest(request);
        ManageMarketingRiskResponse response = rceClient.ManageMarketingRisk(riskRequest);
        log.info("ManageMarketingRisk Response : {}", JsonUtils.toJson(response));

        OutputManageMarketingRisk marketingRisk = response.getData();
        if (marketingRisk == null) {
            throw new ApiAccessException("ManageMarketingRisk result is empty");
        }
        if (0L != marketingRisk.getCode()) {
            throw new ApiAccessException(marketingRisk.getMessage());
        }

        var riskValue = marketingRisk.getValue();
        var riskResult = RiskResult.of(riskValue.getRiskLevel());
        var riskDescriptions = TxRiskDesc.buildDescriptions(riskValue.getRiskType());

        TxRiskResponse riskResponse = new TxRiskResponse();
        riskResponse.setRiskResult(riskResult);
        riskResponse.setRiskDescriptions(riskDescriptions);
        return riskResponse;
    }

    public AccountInfo buildAccountInfo(String openId, String phoneNumber) {
        TxRiskAccountType accountType = TxRiskAccountType.of(openId);
        String phoneNumberMd5 = encodeWithMd5(phoneNumber);

        OtherAccountInfo otherAccountInfo = new OtherAccountInfo();
        otherAccountInfo.setAccountId(phoneNumberMd5);

        WeChatAccountInfo weChatAccountInfo = new WeChatAccountInfo();
        weChatAccountInfo.setWeChatOpenId(openId);
        weChatAccountInfo.setMobilePhone(phoneNumberMd5);

        AccountInfo account = new AccountInfo();
        account.setWeChatAccount(weChatAccountInfo);
        account.setOtherAccount(otherAccountInfo);
        account.setAccountType(accountType.getTxAccountType());
        return account;
    }

    @Nullable
    private String encodeWithMd5(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return null;
        }
        String stripPhoneNumber = StringUtils.deleteWhitespace(phoneNumber);
        return DigestUtils.md5DigestAsHex(stripPhoneNumber.getBytes());
    }

    private ManageMarketingRiskRequest buildRiskRequest(PhoneTxRiskAssessRequest request) {
        // 账号授权信息
        DataAuthorizationInfo authorization = new DataAuthorizationInfo();
        authorization.setDataProviderName(tencentCloudProperties.getDataProviderName());
        authorization.setDataRecipientName(tencentCloudProperties.getDataRecipientName());
        authorization.setIsAuthorize(1L);
        authorization.setUserDataType(new Long[]{1L, 2L, 4L});
        authorization.setIsOrderHandling(1L);

        // 登录用户账号信息
        AccountInfo account = buildAccountInfo(request.getOpenId(), request.getPhoneNumber());

        InputManageMarketingRisk risk = new InputManageMarketingRisk();
        risk.setAccount(account);
        risk.setUserIp(request.getIp());
        risk.setDataAuthorization(authorization);
        risk.setSceneCode(request.getSceneCode()
                .getTxSceneCode());
        risk.setPostTime(Instant.now()
                .getEpochSecond());

        ManageMarketingRiskRequest riskRequest = new ManageMarketingRiskRequest();
        riskRequest.setBusinessSecurityData(risk);
        return riskRequest;
    }

}
