package com.kerryprops.kip.riskcontrol.dto;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 综合风控评估响应DTO.
 * 包含风控评估结果和详细信息.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "综合风控评估响应")
public class ComprehensiveRiskAssessResponse {

    /**
     * 业务场景.
     */
    @Schema(description = "业务场景", example = "LOGIN")
    private String businessScenario;

    /**
     * 商场代码.
     */
    @Schema(description = "商场代码", example = "JAKC")
    private String mallCode;

    /**
     * 用户ID.
     */
    @Schema(description = "用户ID", example = "user123")
    private String userId;

    /**
     * IP地址.
     */
    @Schema(description = "IP地址", example = "************")
    private String ipAddress;

    /**
     * IP地理位置信息.
     */
    @Schema(description = "IP地理位置")
    private IpGeolocationInfo ipGeolocation;

    /**
     * 风控评估结果.
     */
    @Schema(description = "风控评估结果", example = "PASS")
    private RiskResult riskResult;

    /**
     * 拦截提示语.
     */
    @Schema(description = "拦截提示语", example = "缴费地为非中国大陆，暂无法使用会员停车礼遇")
    private String blockMessage;

    /**
     * 评估详情.
     */
    @Schema(description = "评估详情", example = "会员停车权益：非大陆IP被拦截")
    private String assessmentDetails;

    /**
     * 评估时间.
     */
    @Schema(description = "评估时间")
    private LocalDateTime assessmentTime;

    /**
     * 从RiskAssessmentContext创建响应.
     *
     * @param context 风控评估上下文
     * @return ComprehensiveRiskAssessResponse
     */
    public static ComprehensiveRiskAssessResponse fromContext(
            com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext context) {
        ComprehensiveRiskAssessResponse response = new ComprehensiveRiskAssessResponse();

        response.setBusinessScenario(context.getBusinessScenario() != null ?
                context.getBusinessScenario().name() : null);
        response.setMallCode(context.getMallCode() != null ?
                context.getMallCode().getCode() : null);
        response.setUserId(context.getUserId());
        response.setIpAddress(context.getIpAddress());
        response.setRiskResult(context.getRiskResult());
        response.setBlockMessage(context.getBlockMessage());
        response.setAssessmentDetails(context.getAssessmentDetails());
        response.setAssessmentTime(LocalDateTime.now());

        // 设置IP地理位置信息
        if (context.getIpGeolocation() != null) {
            var geolocation = context.getIpGeolocation();
            response.setIpGeolocation(new IpGeolocationInfo(
                    geolocation.getCountry(),
                    geolocation.getCountryIsoCode(),
                    geolocation.getSubdivision(),
                    geolocation.getCity(),
                    geolocation.isValid()
            ));
        }

        return response;
    }

    /**
     * IP地理位置信息内嵌类.
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "IP地理位置信息")
    public static class IpGeolocationInfo {

        @Schema(description = "国家", example = "China")
        private String country;

        @Schema(description = "国家ISO代码", example = "CN")
        private String countryIsoCode;

        @Schema(description = "省份/州", example = "Beijing")
        private String subdivision;

        @Schema(description = "城市", example = "Beijing")
        private String city;

        @Schema(description = "是否有效", example = "true")
        private boolean isValid;
    }
}
