package com.kerryprops.kip.riskcontrol.dto.login;

import com.kerryprops.kip.riskcontrol.constant.RiskResult;
import com.kerryprops.kip.riskcontrol.dto.IpGeolocationInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Login scenario risk assessment response.
 */
@Data
@Schema(description = "Login scenario risk assessment response")
public class LoginRiskAssessResponse {

    @Schema(description = "Mall code", example = "JAKC")
    private String mallCode;

    @Schema(description = "UnionID", example = "unionid123")
    private String unionId;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "IP geolocation information")
    private IpGeolocationInfo ipGeolocation;

    @Schema(description = "Risk assessment result", example = "PASS")
    private RiskResult riskResult;

    @Schema(description = "Block message if rejected")
    private String blockMessage;

    @Schema(description = "Assessment details", example = "Login: Non-mainland IP allowed")
    private String assessmentDetails;

    @Schema(description = "Assessment time")
    private LocalDateTime assessmentTime;
}
