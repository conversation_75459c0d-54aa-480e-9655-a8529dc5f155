package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Optional;

/**
 * MaxMind GeoIP2 implementation of IP geolocation service.
 * Uses MaxMind GeoLite2 database for IP geolocation.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MaxMindGeolocationService implements IpGeolocationService {

    private static final String GEOLITE2_CITY_DB_PATH = "geolite2/GeoLite2-City.mmdb";
    private DatabaseReader databaseReader;

    /**
     * Initializes the MaxMind database reader.
     * This method is called during service initialization.
     */
    private synchronized void initializeDatabaseReader() {
        if (databaseReader != null) {
            return;
        }

        try {
            ClassPathResource resource = new ClassPathResource(GEOLITE2_CITY_DB_PATH);
            if (resource.exists()) {
                databaseReader = new DatabaseReader.Builder(resource.getInputStream()).build();
                log.info("MaxMind GeoLite2 database initialized successfully");
            } else {
                log.warn("MaxMind GeoLite2 database file not found at: {}", GEOLITE2_CITY_DB_PATH);
            }
        } catch (IOException e) {
            log.error("Failed to initialize MaxMind GeoLite2 database", e);
        }
    }

    @Override
    public IpGeolocation geolocateIp(String ipAddress) {
        log.debug("Starting geolocation for IP address: {}", ipAddress);

        if (!isServiceAvailable()) {
            String errorMessage = "MaxMind geolocation service is not available";
            log.warn("{} for IP: {}", errorMessage, ipAddress);
            return IpGeolocation.createInvalid(ipAddress, errorMessage);
        }

        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            CityResponse response = databaseReader.city(inetAddress);

            String country = Optional.ofNullable(response.getCountry().getName()).orElse("Unknown");
            String countryIsoCode = Optional.ofNullable(response.getCountry().getIsoCode()).orElse("Unknown");
            String subdivision = Optional.ofNullable(response.getMostSpecificSubdivision().getName()).orElse("Unknown");
            String city = Optional.ofNullable(response.getCity().getName()).orElse("Unknown");
            Double latitude = Optional.ofNullable(response.getLocation().getLatitude()).orElse(0.0);
            Double longitude = Optional.ofNullable(response.getLocation().getLongitude()).orElse(0.0);

            log.info("Geolocation successful - IP: {}, Country: {} ({}), City: {}",
                    ipAddress, country, countryIsoCode, city);

            return IpGeolocation.createValid(ipAddress, country, countryIsoCode)
                    .withSubdivision(subdivision)
                    .withCity(city)
                    .withCoordinates(latitude, longitude);

        } catch (UnknownHostException e) {
            String errorMessage = "Invalid IP address format";
            log.warn("{}: {} - {}", errorMessage, ipAddress, e.getMessage());
            return IpGeolocation.createInvalid(ipAddress, errorMessage);
        } catch (GeoIp2Exception e) {
            String errorMessage = "IP address not found in geolocation database";
            log.warn("{}: {} - {}", errorMessage, ipAddress, e.getMessage());
            return IpGeolocation.createInvalid(ipAddress, errorMessage);
        } catch (IOException e) {
            String errorMessage = "Database access error during geolocation";
            log.error("{}: {} - {}", errorMessage, ipAddress, e.getMessage(), e);
            return IpGeolocation.createInvalid(ipAddress, errorMessage);
        }
    }

    @Override
    public boolean isServiceAvailable() {
        if (databaseReader == null) {
            initializeDatabaseReader();
        }
        return databaseReader != null;
    }

    /**
     * Closes the database reader when the service is destroyed.
     * This method is called during application shutdown.
     */
    public void destroy() {
        if (databaseReader != null) {
            try {
                databaseReader.close();
                log.info("MaxMind database reader closed successfully");
            } catch (IOException e) {
                log.warn("Error closing MaxMind database reader", e);
            }
        }
    }
}
