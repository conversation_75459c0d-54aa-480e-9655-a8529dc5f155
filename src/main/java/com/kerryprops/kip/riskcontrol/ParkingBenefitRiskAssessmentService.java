package com.kerryprops.kip.riskcontrol.service.parking;

import com.kerryprops.kip.riskcontrol.dto.parking.ParkingBenefitRiskAssessRequest;
import com.kerryprops.kip.riskcontrol.dto.parking.ParkingBenefitRiskAssessResponse;
import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.service.ComprehensiveRiskAssessmentService;
import com.kerryprops.kip.riskcontrol.service.base.BaseRiskAssessmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for parking benefits risk assessment.
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "drools.enabled", havingValue = "true", matchIfMissing = true)
public class ParkingBenefitRiskAssessmentService extends BaseRiskAssessmentService {

    private final ComprehensiveRiskAssessmentService comprehensiveRiskAssessmentService;

    /**
     * Assess parking benefits risk.
     */
    public ParkingBenefitRiskAssessResponse assessParkingBenefitRisk(ParkingBenefitRiskAssessRequest request) {
        log.info("Starting parking benefit risk assessment - mall: {}, IP: {}",
                request.getMallCode(), request.getIpAddress());

        // Validate request
        validateCommonParameters(request.getMallCode());

        // Create context
        RiskAssessmentContext context = createBaseRiskContext(
                request.getMallCode(),
                RiskAssessmentContext.BusinessScenario.PARKING_BENEFIT
        );

        // Add IP info
        if (request.getIpAddress() != null) {
            context = withIpInfo(context, request.getIpAddress(), null);
        }

        // Add member info
        context = withMemberInfo(context, request.getMemberPoints(), request.getIsFrozenMember());

        // Assess risk
        var assessedContext = comprehensiveRiskAssessmentService.assessRisk(context);

        // Convert to response
        ParkingBenefitRiskAssessResponse response = new ParkingBenefitRiskAssessResponse();
        response.setMallCode(assessedContext.getMallCode().getCode());
        response.setIpAddress(assessedContext.getIpAddress());
        response.setMemberPoints(assessedContext.getMemberPoints());
        response.setRiskResult(assessedContext.getRiskResult());
        response.setBlockMessage(assessedContext.getBlockMessage());
        response.setAssessmentDetails(assessedContext.getAssessmentDetails());
        response.setAssessmentTime(java.time.LocalDateTime.now());

        if (assessedContext.getIpGeolocation() != null) {
            response.setIpGeolocation(convertIpGeolocation(assessedContext.getIpGeolocation()));
        }

        log.info("Parking benefit risk assessment complete - mall: {}, result: {}, details: {}",
                request.getMallCode(), response.getRiskResult(), response.getAssessmentDetails());

        return response;
    }

    private com.kerryprops.kip.riskcontrol.dto.IpGeolocationInfo convertIpGeolocation(
            com.kerryprops.kip.riskcontrol.model.IpGeolocation geolocation) {
        return new com.kerryprops.kip.riskcontrol.dto.IpGeolocationInfo(
                geolocation.getCountry(),
                geolocation.getCountryIsoCode(),
                geolocation.getSubdivision(),
                geolocation.getCity(),
                geolocation.isValid()
        );
    }
}
