package com.kerryprops.kip.riskcontrol.dto.photo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Photo points risk assessment request.
 */
@Data
@Schema(description = "Photo points risk assessment request")
public class PhotoPointsRiskAssessRequest {

    @NotBlank(message = "Mall code is required")
    @Schema(
            description = "Mall code",
            example = "JAKC",
            allowableValues = {"JAKC", "HKC", "KP", "ALL"}
    )
    private String mallCode;

    @Schema(description = "IP address", example = "************")
    private String ipAddress;

    @Schema(description = "Phone number", example = "13800138000")
    private String phoneNumber;

    @Schema(description = "Member points", example = "1000")
    private Integer memberPoints;

    @Schema(description = "Is member frozen", example = "false")
    private Boolean isFrozenMember;
}
