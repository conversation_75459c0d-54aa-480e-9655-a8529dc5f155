package com.kerryprops.kip.riskcontrol.service;

import com.kerryprops.kip.riskcontrol.model.IpGeolocation;

/**
 * Service interface for IP geolocation operations.
 * Provides methods to determine geographic location of IP addresses.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
public interface IpGeolocationService {

    /**
     * Determines the geographic location of an IP address.
     *
     * @param ipAddress the IP address to geolocate
     * @return IpGeolocation containing geographic information
     */
    IpGeolocation geolocateIp(String ipAddress);

    /**
     * Checks if the geolocation service is available and properly configured.
     *
     * @return true if the service is available, false otherwise
     */
    boolean isServiceAvailable();
}
