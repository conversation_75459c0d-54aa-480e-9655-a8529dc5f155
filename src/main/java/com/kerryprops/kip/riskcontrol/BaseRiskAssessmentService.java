package com.kerryprops.kip.riskcontrol.service.base;

import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Base service providing common risk assessment functionality.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public abstract class BaseRiskAssessmentService {
    
    private RiskAssessmentContext.MallCode parseMallCode(String mallCode) {
        try {
            return RiskAssessmentContext.MallCode.valueOf(mallCode);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid mall code: {}", mallCode);
            return RiskAssessmentContext.MallCode.ALL;
        }
    }

    /**
     * Create base risk context.
     */
    protected RiskAssessmentContext createBaseRiskContext(String mallCode, RiskAssessmentContext.BusinessScenario scenario) {
        RiskAssessmentContext.MallCode mall = parseMallCode(mallCode);
        return RiskAssessmentContext.create(scenario, mall);
    }

    /**
     * Add IP information to context.
     */
    protected RiskAssessmentContext withIpInfo(RiskAssessmentContext context, String ipAddress, IpGeolocation geolocation) {
        return context.withIpInfo(ipAddress, geolocation);
    }

    /**
     * Add member information to context.
     */
    protected RiskAssessmentContext withMemberInfo(RiskAssessmentContext context, Integer points, Boolean isFrozen) {
        return context.withMemberInfo(points, isFrozen);
    }

    /**
     * Validate common parameters.
     */
    protected void validateCommonParameters(String mallCode) {
        if (mallCode == null || mallCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Mall code must not be null or empty");
        }
    }
}
