package com.kerryprops.kip.riskcontrol.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model representing IP geolocation information.
 * Contains geographic details extracted from IP address lookup.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IpGeolocation {

    /**
     * The IP address that was geolocated.
     */
    private String ipAddress;

    /**
     * The country name where the IP is located.
     */
    private String country;

    /**
     * The ISO country code (e.g., "CN" for China).
     */
    private String countryIsoCode;

    /**
     * The subdivision/state/province name.
     */
    private String subdivision;

    /**
     * The city name.
     */
    private String city;

    /**
     * Latitude coordinate.
     */
    private Double latitude;

    /**
     * Longitude coordinate.
     */
    private Double longitude;

    /**
     * Indicates if the geolocation lookup was successful.
     */
    private boolean isValid;

    /**
     * Error message if geolocation failed.
     */
    private String errorMessage;

    /**
     * Creates a valid geolocation result.
     *
     * @param ipAddress      the IP address
     * @param country        the country name
     * @param countryIsoCode the country ISO code
     * @return IpGeolocation builder for setting additional properties
     */
    public static IpGeolocation createValid(String ipAddress, String country, String countryIsoCode) {
        IpGeolocation geolocation = new IpGeolocation();
        geolocation.setIpAddress(ipAddress);
        geolocation.setCountry(country);
        geolocation.setCountryIsoCode(countryIsoCode);
        geolocation.setValid(true);
        geolocation.setErrorMessage(null);
        return geolocation;
    }

    /**
     * Creates an invalid geolocation result with error message.
     *
     * @param ipAddress    the IP address
     * @param errorMessage the error message
     * @return IpGeolocation with error information
     */
    public static IpGeolocation createInvalid(String ipAddress, String errorMessage) {
        IpGeolocation geolocation = new IpGeolocation();
        geolocation.setIpAddress(ipAddress);
        geolocation.setValid(false);
        geolocation.setErrorMessage(errorMessage);
        return geolocation;
    }

    /**
     * Sets the subdivision for this geolocation.
     *
     * @param subdivision the subdivision name
     * @return this IpGeolocation instance for method chaining
     */
    public IpGeolocation withSubdivision(String subdivision) {
        this.subdivision = subdivision;
        return this;
    }

    /**
     * Sets the city for this geolocation.
     *
     * @param city the city name
     * @return this IpGeolocation instance for method chaining
     */
    public IpGeolocation withCity(String city) {
        this.city = city;
        return this;
    }

    /**
     * Sets the coordinates for this geolocation.
     *
     * @param latitude  the latitude
     * @param longitude the longitude
     * @return this IpGeolocation instance for method chaining
     */
    public IpGeolocation withCoordinates(Double latitude, Double longitude) {
        this.latitude = latitude;
        this.longitude = longitude;
        return this;
    }

    /**
     * Checks if this IP is from mainland China.
     *
     * @return true if the IP is from mainland China (CN), false otherwise
     */
    public boolean isFromMainlandChina() {
        return isValid && "CN".equalsIgnoreCase(countryIsoCode);
    }
}
