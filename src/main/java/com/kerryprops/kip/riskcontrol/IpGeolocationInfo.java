package com.kerryprops.kip.riskcontrol.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IP geolocation information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP geolocation information")
public class IpGeolocationInfo {

    @Schema(description = "Country", example = "China")
    private String country;

    @Schema(description = "Country ISO code", example = "CN")
    private String countryIsoCode;

    @Schema(description = "Province/State", example = "Beijing")
    private String subdivision;

    @Schema(description = "City", example = "Beijing")
    private String city;

    @Schema(description = "Is valid", example = "true")
    private boolean isValid;
}
