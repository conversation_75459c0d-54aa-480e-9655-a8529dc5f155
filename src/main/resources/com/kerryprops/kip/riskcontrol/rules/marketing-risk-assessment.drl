package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;

/**
 * Risk assessment rules specific to MARKETING business scenario.
 * 
 * Business Rules:
 * - 负积分 -> N (拦截)
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */

// ========== 积分相关规则 ==========

// 营销场景：负积分 -> N (拦截)
rule "Marketing - Negative Points - REJECT"
    salience 80
    when
        $context : RiskAssessmentContext(
            businessScenario == RiskAssessmentContext.BusinessScenario.MARKETING,
            memberPoints != null,
            memberPoints < 0,
            mallCode == RiskAssessmentContext.MallCode.JAKC || mallCode == RiskAssessmentContext.MallCode.HKC || mallCode == RiskAssessmentContext.MallCode.KP,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("营销场景：负积分被拦截");
        System.out.println("Rule executed: Marketing - Negative Points - REJECT");
end
