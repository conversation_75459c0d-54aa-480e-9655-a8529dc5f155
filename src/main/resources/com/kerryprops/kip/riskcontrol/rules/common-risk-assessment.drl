package com.kerryprops.kip.riskcontrol.rules;

import com.kerryprops.kip.riskcontrol.model.RiskAssessmentContext;
import com.kerryprops.kip.riskcontrol.model.IpGeolocation;
import com.kerryprops.kip.riskcontrol.constant.RiskResult;

/**
 * Common risk assessment rules that apply across all business scenarios.
 * These rules include shared member attributes, Tencent risk controls, and default fallback rules.
 *
 * <AUTHOR> Properties Limited
 * @since 1.0
 */

// ========== 会员属性规则 ==========

// 冻结会员 -> N (拦截)
rule "Frozen Member - REJECT"
    salience 60
    when
        $context : RiskAssessmentContext(
            isFrozenMember == true,
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("当前会员卡无法使用");
        $context.setAssessmentDetails("冻结会员被拦截");
        System.out.println("Rule executed: Frozen Member - REJECT");
end

// ========== 腾讯风控规则 ==========

// 腾讯风控：高风险 -> N (拦截)
rule "Tencent Risk High - REJECT"
    salience 50
    when
        $context : RiskAssessmentContext(
            tencentRiskLevel == "高",
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.setAssessmentDetails("腾讯风控：高风险被拦截");
        System.out.println("Rule executed: Tencent Risk High - REJECT");
end

// 腾讯风控：中风险 -> 手机验证码拦截
rule "Tencent Risk Medium - SMS Block"
    salience 50
    when
        $context : RiskAssessmentContext(
            tencentRiskLevel == "中",
            mallCode == RiskAssessmentContext.MallCode.ALL,
            riskResult == null
        )
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setBlockMessage("无提示，收不到验证码");
        $context.setAssessmentDetails("腾讯风控：中风险手机验证码拦截");
        System.out.println("Rule executed: Tencent Risk Medium - SMS Block");
end

// ========== 默认规则 ==========

// 大陆IP默认通过规则
rule "Mainland IP Default - PASS"
    salience 10
    when
        $context : RiskAssessmentContext(
            ipGeolocation != null,
            riskResult == null
        )
        $geolocation : IpGeolocation(fromMainlandChina == true) from $context.ipGeolocation
    then
        $context.setRiskResult(RiskResult.PASS);
        $context.setAssessmentDetails("大陆IP默认通过");
        System.out.println("Rule executed: Mainland IP Default - PASS");
end

// 最终默认拒绝规则
rule "Default REJECT"
    salience -100  // 最低优先级
    when
        $context : RiskAssessmentContext(riskResult == null)
    then
        $context.setRiskResult(RiskResult.REJECT);
        $context.setAssessmentDetails("默认拒绝：未匹配到任何通过规则");
        System.out.println("Rule executed: Default REJECT");
end
